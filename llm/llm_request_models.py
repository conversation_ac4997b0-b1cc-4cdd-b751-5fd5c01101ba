from dataclasses import dataclass
from typing import List, Dict, Optional, Any

from llm.models import TopicObject


# Constants for analysis result keys
TEXT_IDX_KEY = "text_idx"
TOPIC_IDX_KEY = "topic_idx"
SCORE_KEY = "score"
SENTIMENT_KEY = "sentiment"
RESULTS_KEY = "results"

VERSION = "0.0.3"


@dataclass
class AnalysisRequest:
    """Request data for text analysis"""
    messages: List[Dict[str, str]]
    model: str = "gpt-4o"
    temperature: float = 0
    functions: List[Dict] = None
    function_call: Dict[str, str] = None
    api_key: Optional[str] = None


@dataclass
class Message:
    """Chat message structure"""
    role: str
    content: str


class TextAnalysisRequestBuilder:
    """Helper class to build LLM requests for text analysis"""

    SENTIMENT_MAP = {
        -1: "negative",
        0: "negative",
        1: "neutral",
        2: "positive"
    }

    @staticmethod
    def create_function_definition(texts_count: int, topics_count: int) -> dict:
        """Create function definition for text analysis dynamically based on the number of texts and topics"""
        return {
            "name": "analyze_texts",
            "description": "Analyze multiple texts for multiple topics",
            "parameters": {
                "type": "object",
                "properties": {
                    RESULTS_KEY: {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                TEXT_IDX_KEY: {
                                    "type": "integer",
                                    "minimum": 0,
                                    "maximum": texts_count - 1
                                },
                                TOPIC_IDX_KEY: {
                                    "type": "integer",
                                    "minimum": 0,
                                    "maximum": topics_count - 1
                                },
                                SCORE_KEY: {
                                    "type": "number",
                                    "minimum": 0,
                                    "maximum": 1
                                },
                                SENTIMENT_KEY: {
                                    "type": "integer",
                                    "minimum": 0,
                                    "maximum": 2
                                }
                            },
                            "required": [TEXT_IDX_KEY, TOPIC_IDX_KEY, SCORE_KEY, SENTIMENT_KEY]
                        },
                        "minItems": texts_count * topics_count,
                        "maxItems": texts_count * topics_count
                    }
                },
                "required": [RESULTS_KEY]
            }
        }

    @staticmethod
    def create_batch_message(texts: List[str], topics: List[TopicObject]) -> Message:
        """Create message for batch analysis with TopicObject"""
        # Format topics with additional metadata
        topics_formatted = ""
        for i, topic_obj in enumerate(topics):
            topics_formatted += f"{i}. Topic: {topic_obj.topic}\n"
            topics_formatted += f"   Category: {topic_obj.category}\n"
            topics_formatted += f"   Description: {topic_obj.description}\n\n"

        texts_formatted = "\n\n".join(f"Text {i}:\n{text}" for i, text in enumerate(texts))

        message_content = (
            f"Analyze exactly {len(texts)} texts for ALL of the following {len(topics)} topics "
            f"(reference topics by their index number):\n\n"
            f"Topics:\n{topics_formatted}\n\n"
            f"Texts to analyze:\n{texts_formatted}\n\n"
            f"SCORING GUIDELINES:\n"
            f"- Assign scores with extreme precision based on DIRECT RELEVANCE to the topic\n"
            f"- Score 0: Text has NO relevance to the topic whatsoever\n"
            f"- Score 0.1-0.3: Text has MINIMAL relevance to the topic\n"
            f"- Score 0.4-0.6: Text has MODERATE relevance to the topic\n"
            f"- Score 0.7-0.9: Text has STRONG relevance to the topic\n"
            f"- Score 1.0: Text has PERFECT relevance to the topic\n"
            f"- If a text does not explicitly mention or strongly imply a topic, assign a score of 0\n"
            f"- Do not assign scores based on tangential or indirect connections\n"
            f"- Be extremely discriminating in your scoring\n\n"
            f"SENTIMENT GUIDELINES:\n"
            f"- Sentiment 0: Text expresses a NEGATIVE sentiment toward the topic\n"
            f"- Sentiment 1: Text expresses a NEUTRAL sentiment toward the topic\n"
            f"- Sentiment 2: Text expresses a POSITIVE sentiment toward the topic\n\n"
            f"CRITICAL REQUIREMENTS:\n"
            f"1. You MUST analyze EVERY topic for EACH text\n"
            f"2. Text indices MUST be between 0 and {len(texts) - 1} inclusive\n"
            f"3. Topic indices MUST be between 0 and {len(topics) - 1} inclusive\n"
            f"4. You MUST return analysis for EXACTLY {len(texts)} texts\n"
            f"5. Each text MUST have EXACTLY {len(topics)} topic analyses\n"
            f"6. You MUST assign a score of 0 for topics that are not relevant to the text\n\n"
            f"FINAL VALIDATION:\n"
            f"- Verify you have exactly {len(texts)} text entries\n"
            f"- Verify each text has exactly {len(topics)} topic entries\n"
            f"- Verify no index exceeds the valid ranges\n"
            f"- Double-check that scores of 0 are assigned to irrelevant topics\n"
            f"- Return ONLY the JSON object without any additional explanation or commentary"
        )

        return Message(role="user", content=message_content)

    @classmethod
    def prepare_llm_request(
            cls,
            texts: List[str],
            topics: List[TopicObject],
            llm_model: str,
            api_key: Optional[str] = None
    ) -> AnalysisRequest:
        """Prepare the LLM request with necessary parameters for TopicObject"""
        function = cls.create_function_definition(len(texts), len(topics))

        system_message = Message(
            role="system",
            content=(
                "You are a precise text analysis assistant that evaluates texts against specific topics. "
                "You provide structured output in compact JSON format. "
                "You must analyze ALL provided topics for EACH text with extreme precision. "
                "For each topic, you will be provided with detailed information "
                "including category and description that will help you better understand the topic. "
                "Your analysis must be highly accurate and discriminating. "
                "You should assign scores based on DIRECT RELEVANCE to the topic, not tangential connections. "
                "A score of 0 means NO relevance to the topic. "
                "A score of 0.1-0.3 means MINIMAL relevance to the topic. "
                "A score of 0.4-0.6 means MODERATE relevance to the topic. "
                "A score of 0.7-0.9 means STRONG relevance to the topic. "
                "A score of 1.0 means PERFECT relevance to the topic. "
                "If a text does not explicitly mention or strongly imply a topic, assign a score of 0. "
                "For sentiment analysis, use the following guidelines: "
                "0 for negative sentiment, 1 for neutral sentiment, and 2 for positive sentiment."
            )
        )

        messages = [
            system_message,
            cls.create_batch_message(texts, topics)
        ]

        return AnalysisRequest(
            messages=[{"role": m.role, "content": m.content} for m in messages],
            api_key=api_key,
            model=llm_model,
            temperature=0,
            functions=[function],
            function_call={"name": "analyze_texts"}
        )
